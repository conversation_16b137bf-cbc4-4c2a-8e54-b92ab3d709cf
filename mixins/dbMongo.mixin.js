const DbService = require('moleculer-db');
const MongooseAdapter = require('moleculer-db-adapter-mongoose');

module.exports = function(mongooseModel) {
  const mongoDbUri =
    // process.env.MONGO_URI || "mongodb+srv://stearlersmile:<EMAIL>/demo";
    // process.env.MONGO_URI || "mongodb+srv://stealersmile:<EMAIL>/demo?tls=true&authMechanism=SCRAM-SHA-256&retrywrites=false&maxIdleTimeMS=120000";
    process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/demo';
  // process.env.MONGO_URI || 'mongodb://demo:xuanhung2801@103.152.165.178:27017/demo'

  return {
    mixins: [DbService],
    adapter: new MongooseAdapter(mongoDbUri),
    model: mongooseModel,
    actions: {
      create: {
        visibility: 'published',
      },
      update: {
        visibility: 'published',
      },
      list: {
        visibility: 'published',
      },
      get: {
        visibility: 'published',
      },
      remove: {
        visibility: 'published',
      },
      insertMany: {
        async handler(ctx) {
          return this.adapter.insertMany(ctx.params);
        },
      },
    },
  };
};
