const nodemailer = require('nodemailer');
const {getConfig} = require('../config/config');
const config = getConfig(process.env.NODE_ENV);

exports.sendEmail = (mailOptions) => {
  try {
    let transporter = nodemailer.createTransport(config.mail);
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.log('error===================', error);
        // callback(error);
      } else {
        // callback(undefined, info);
      }
    });
  } catch (e) {
    console.log('aaaaaaaaaaaaaaaaa===================', e);
    console.log(e);
  }
};
