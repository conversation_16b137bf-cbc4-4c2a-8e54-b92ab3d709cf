const config = {
  production: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'https://demo.stealersmile.click',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: `${process.env.DOMAIN || "https://demo.stealersmile.click"}/auth/login-google`,
    backend_base_url: 'http://localhost:3000',
    supportEmail: "<EMAIL>",
    mail: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'ymxi simf kpyf rolx',
      },
      tls: {
        rejectUnauthorized: false
      }
    },
  },
  development: {
    websiteName: 'Demo',
    supportEmail: "<EMAIL>",
    mail: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'ymxi simf kpyf rolx',
      },
      tls: {
        rejectUnauthorized: false
      }
    },
    domain: process.env.DOMAIN || 'http://localhost:8080',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: "http://localhost:8080/auth/login-google",
    backend_base_url: '',
  },
};

exports.getConfig = env => config[env] || config.development;
