const {google} = require('googleapis');
const MODEL = require('./drives.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const {GoogleAuth} = require('google-auth-library');
const fs = require('fs');
const path = require('path');
const FileMixin = require('../../../mixins/file.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {PassThrough} = require('stream');

const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffmpeg = require('fluent-ffmpeg');
ffmpeg.setFfmpegPath(ffmpegPath);

const storageDir = path.join(__dirname, 'storage');

module.exports = {
  name: 'drives',
  mixins: [DbMongoose(MODEL), BaseService, AdminService, FileMixin],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    uploadMovie: {
      rest: 'POST /movie',
      async handler(ctx) {
        const {movieId} = ctx.params;
        const {movie, episodes} = await ctx.call('movies.get', {id: movieId});
        if (!movie || !episodes || episodes.length === 0) {
          throw new MoleculerClientError('Movie or episodes not found');
        }

        const authClient = await this.authenticate();
        const drive = google.drive({version: 'v3', auth: authClient});

        const folderId = await this.getFolderDrive(drive, movie.slug);
        if (!folderId) {
          throw new MoleculerClientError(`Failed to get or create Google Drive folder for movie: ${movie.slug}`);
        }

        const uploadedFilesData = [];

        for (const episode of episodes) {
          const uploadPromises = episode.link.map((videoUrl, i) =>
            // this.uploadFileToDrive(drive, folderId, movie.slug, episode, videoUrl, i)
            this.streamVideoToDrive(drive, folderId, movie.slug, episode, videoUrl, i),
          );

          const fileResults = await Promise.all(uploadPromises);
          uploadedFilesData.push(...fileResults);
        }

        return uploadedFilesData;
      },
    },
  },

  methods: {
    async authenticate() {
      try {
        const keyFilePath = path.join(__dirname, 'credential.json');
        const auth = new GoogleAuth({
          keyFilename: keyFilePath,
          scopes: ['https://www.googleapis.com/auth/drive.file'],
          cache: true,
        });
        return await auth.getClient();
      } catch (err) {
        throw new Error(`Google Drive authentication failed: ${err.message}`);
      }
    },
    async getFolderDrive(drive, folderName) {
      const parentFolderId = '194VCeKNYYQJL4eaMRInSVqjxEhouWIQw';

      try {
        const query = {
          q: `mimeType='application/vnd.google-apps.folder' and name='${folderName}' and '${parentFolderId}' in parents and trashed=false`,
          fields: 'files(id, name)',
          spaces: 'drive',
        };

        const {data: {files}} = await drive.files.list(query);

        if (files?.length > 0) {
          return files[0].id;
        }

        const {data: {id}} = await drive.files.create({
          requestBody: {
            name: folderName,
            mimeType: 'application/vnd.google-apps.folder',
            parents: [parentFolderId],
          },
          fields: 'id',
        });
        return id;

      } catch (error) {
        return null;
      }
    },
    async uploadFileToDrive(drive, folderId, movieSlug, episode, videoUrl, videoIndex) {
      const videoName = `${movieSlug}-${episode.name}-${videoIndex}.mp4`;
      const videoPath = this.getFilePath(videoName, this.getDirPath(movieSlug, storageDir));

      try {
        await this.saveVideo(videoUrl, videoPath);

        const {data} = await drive.files.create({
          requestBody: {
            name: videoName,
            parents: [folderId],
          },
          media: {
            mimeType: 'video/mp4',
            body: fs.createReadStream(videoPath),
          },
          fields: 'id, name, webViewLink',
        });

        await this.adapter.insert({
          episodeId: episode._id,
          fileId: data.id,
          fileName: data.name,
          webViewLink: data.webViewLink,
        });

        return data;

      } catch (error) {
        throw new MoleculerClientError('Failed to upload video', 500, 'UPLOAD_ERROR');
      } finally {
        try {
          if (fs.existsSync(videoPath)) {
            await fs.promises.unlink(videoPath);
          }
        } catch (err) {
          this.logger.error(`Failed to delete temp file ${videoPath}: ${err.message}`);
        }
      }
    },

    async streamVideoToDrive(drive, folderId, movieSlug, episode, videoUrl, videoIndex) {
      const videoName = `${movieSlug}-${episode.name}-${videoIndex}.mp4`;

      const passThroughStream = new PassThrough();
      let ffmpegCommand = null;
      let ffmpegErrored = false;

      try {
        ffmpegCommand = ffmpeg(videoUrl)
          .outputFormat('mp4')
          .outputOptions([
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-bsf:a', 'aac_adtstoasc',
            '-f', 'mpegts',
            '-analyzeduration', '10M',
            '-probesize', '10M',
            '-reconnect', '1',
            '-reconnect_streamed', '1',
            '-reconnect_delay_max', '5',
            '-movflags', '+faststart',
            '-max_muxing_queue_size', '1024',
          ])
          .on('progress', (progress) => {
            if (progress.percent && progress.percent >= 0) {
              this.logger.debug(`FFmpeg progress: ${progress.percent.toFixed(2)}%`);
            }
          })
          .on('error', (err, stdout, stderr) => {
            ffmpegErrored = true;
            this.logger.error(`FFmpeg error for ${videoName}:`, err, {stdout, stderr});
          })
          .on('end', () => {
            this.logger.info(`Upload should complete shortly.`);
          })
          .pipe(passThroughStream, {end: true});

        const media = {
          mimeType: 'video/mp4',
          body: passThroughStream,
        };

        const file = await drive.files.create({
          requestBody: {
            name: videoName,
            parents: [folderId],
          },
          media: media,
          fields: 'id, name, webViewLink',
        });

        if (ffmpegErrored) {
          try {
            await drive.files.delete({fileId: file.data.id});
          } catch (error) {
          }
          throw new MoleculerClientError(`FFmpeg error occurred during upload process.`, 500, 'UPLOAD_FAIL');
        }

        await this.adapter.insert({
          episodeId: episode._id,
          fileId: file.data.id,
          fileName: file.data.name,
          webViewLink: file.data.webViewLink,
        });
        return file.data;
      } catch (error) {
        throw error;
      }
    },
  },

  events: {
    sseEventName(jobId) {
      return `sse.movie_status.${jobId}`;
    },
  },
};
