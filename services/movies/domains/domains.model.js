const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {DOMAIN} = require('../../../constants/dbCollections');

const schema = new mongoose.Schema({
  name: {type: String},
  apiUrl: {type: String},
  movieUrl: {type: String},
  config: {type: mongoose.Schema.Types.Mixed},
  homeUrl: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(DOMAIN, schema, DOMAIN);
