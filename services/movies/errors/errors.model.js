const mongoose = require('mongoose');
const {ERROR, MOVIE} = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new mongoose.Schema({
  movieId: {type: mongoose.Schema.Types.ObjectId, ref: MOVIE},
  name: {type: String},
  link: {type: String},
  error: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ERROR, schema, ERROR);

