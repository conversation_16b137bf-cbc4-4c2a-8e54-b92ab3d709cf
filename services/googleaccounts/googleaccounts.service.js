const fs = require('fs');
const Model = require('./googleaccounts.model');
const BaseService = require('../../mixins/baseService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {getConfig} = require('../../config/config');
const config = getConfig(process.env.NODE_ENV);
const DbMongoose = require('../../mixins/dbMongo.mixin');
const FileMixin = require('../../mixins/file.mixin');
const {transformFile} = require('../../helpers/excelHelper');
const AdminService = require('../../mixins/adminService.mixin');

const ConstData = {
  TAI_KHOAN: '<PERSON><PERSON><PERSON> k<PERSON>n',
  MAT_KHAU: '<PERSON><PERSON><PERSON> khẩu',
  NGAY_TAO: '<PERSON><PERSON>y tạo ',
  F2A: 'F2A code',
  TINH_TRANG: 'Tình trạng',
  THE_LOAI: 'Thể loại',
  NGAY_GUI_MAIL: 'Ngày gửi mail',
};

module.exports = {
  name: 'googleaccounts',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    upload: {
      admin: true,
      async handler(ctx) {
        const {filename, mimetype} = ctx.meta;
        const uniqueFileName = this.createUniqueFileName(filename);

        const data = await transformFile(ctx.params);

        const dataToDB = data.flatMap(item => item.rows.map(row => ({
          email: row[ConstData.TAI_KHOAN],
          password: row[ConstData.MAT_KHAU],
          status: row[ConstData.TINH_TRANG],
          type: row[ConstData.THE_LOAI],
          f2a: row[ConstData.F2A],
          sendMailAt: row[ConstData.NGAY_GUI_MAIL],
        }))).filter(item => item.email);

        return Model.bulkWrite(dataToDB.map(item => ({
          updateOne: {
            filter: {email: item.email},
            update: {$set: item},
            upsert: true,
          },
        })));
      },
    },
  },

  methods: {},
  events: {},
};
