const {GOOGLE_ACCOUNT} = require('../../constants/dbCollections');
const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const {Schema} = mongoose;
const schema = new Schema({
  email: {type: String},
  password: {type: String},
  f2a: {type: String},
  status: {type: String},
  type: {type: String},
  sendMailAt: {type: Date},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(GOOGLE_ACCOUNT, schema, GOOGLE_ACCOUNT);
