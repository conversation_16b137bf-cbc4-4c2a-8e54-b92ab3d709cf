const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {SETTING} = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  telegramBotToken: {type: String},
  telegramChatId: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(SETTING, schema, SETTING);
