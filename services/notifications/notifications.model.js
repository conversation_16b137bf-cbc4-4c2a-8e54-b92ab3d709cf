const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {NOTIFICATION} = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  cronTime: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(NOTIFICATION, schema, NOTIFICATION);
