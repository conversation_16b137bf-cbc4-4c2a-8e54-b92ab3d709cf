const DbMongoose = require('../../mixins/dbMongo.mixin');
const MODEL = require('./notifications.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');

module.exports = {
  name: 'notifications',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {},

  methods: {},

  events: {},

  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
