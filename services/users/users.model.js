const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {USER} = require('../../constants/dbCollections');
const {encryptPassword} = require('../../helpers/usersHelper');

const schema = new mongoose.Schema({
  fullName: {type: String, trim: true, default: 'New Account'},
  email: {type: String, trim: true, unique: true, index: true, lowercase: true, required: 'Please fill in an email'},
  password: {type: String},
  isDeleted: {type: Boolean, default: false, select: false},
  isSystemAdmin: {type: Boolean, default: false},
  active: {type: Boolean, default: false},
  lastChangePassword: {type: Date, default: new Date()},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.pre('save', function(next) {
  let user = this;
  if (!user.isModified('password')) return next();
  user.password = encryptPassword(user.password);
  next();
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(USER, schema, USER);
