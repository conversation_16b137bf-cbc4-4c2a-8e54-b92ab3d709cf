const moment = require('moment');
const DbMongoose = require('../../mixins/dbMongo.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const USER = require('./users.model');
const i18next = require('i18next');
const {sendEmail} = require('../../helpers/emailHelper');
const {getConfig} = require('../../config/config');
const config = getConfig(process.env.NODE_ENV);
const {comparePassword, encryptPassword} = require('../../helpers/usersHelper');
const jwt = require('../../helpers/jwt');
const BaseService = require('../../mixins/baseService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const cookie = require('cookie');
const EmailTemplate = require('./emailtemplate/emailtemplate');
const axios = require('axios');
const AdminService = require('../../mixins/adminService.mixin');

module.exports = {
  name: 'users',
  mixins: [DbMongoose(USER), BaseService, FunctionsCommon, AdminService],
  settings: {
    SECRET: process.env.SECRET || 'secret',
    RESET_PASSWORD_SECRET: process.env.RESET_PASSWORD_SECRET || 'reset-password-secret',
    ACTIVATION_SECRET: process.env.ACTIVATION_SECRET || 'activation-secret',

    fields: ['_id', 'email', 'fullName', 'active', 'isSystemAdmin', 'hasPassword'],
    populates: {},
    populateOptions: [],
  },

  actions: {
    login: {
      rest: 'POST /login',
      params: {
        email: {type: 'string'},
        password: {type: 'string', min: 1},
      },
      skipToken: true,
      async handler(ctx) {
        let {email, password} = ctx.params;
        let user = await this.adapter.findOne({email: email, isDeleted: false});

        if (!user) {
          throw new MoleculerClientError(i18next.t('login_fail'));
        }

        let authenticated;
        if (!authenticated && user?.password) {
          authenticated = comparePassword(password, user.password);
        }
        if (!authenticated) {
          throw new MoleculerClientError(i18next.t('login_fail'));
        }

        if (!user.active) {
          throw new MoleculerClientError(i18next.t('error_account_not_active'), 403);
        }

        const accessToken = jwt.issue({id: user._id, isUser: true}, '72h', this.settings.SECRET);
        const refreshToken = await this.refreshTokenCreator(ctx, user);

        this.setTokenToCookies(ctx, accessToken, refreshToken);
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      },
    },
    loginGoogle: {
      rest: 'POST /google',
      skipToken: true,
      async handler(ctx) {
        const {code} = ctx.params;
        const data = await this.getOauthGoogleToken(code);
        const {id_token, access_token} = data;

        const googleUser = await this.getGoogleUser(id_token, access_token);
        if (!googleUser.verified_email) {
          throw new MoleculerClientError(i18next.t('google_email_not_verified'));
        }

        let user = await ctx.call('users.findOne', {email: googleUser.email});

        if (!user) {
          user = await this.actions.register({email: googleUser.email, fullName: googleUser.name});
        }

        if (user.message) {
          throw new MoleculerClientError(user.message);
        }

        if (!user.active) {
          await this.adapter.updateById(user._id, {active: true});
        }

        const accessToken = jwt.issue({id: user._id, isUser: true}, '24h', this.settings.SECRET);
        const refreshToken = await this.refreshTokenCreator(ctx, user);

        this.setTokenToCookies(ctx, accessToken, refreshToken);
        return await this.transformDocuments(ctx, {}, user);
      },
    },
    findOne: {
      rest: 'GET /findOne',
      params: {
        email: {type: 'string'},
      },
      async handler(ctx) {
        return this.adapter.findOne({...ctx.params});
      },
    },
    forgotPasswordMail: {
      rest: 'POST /forgotPassword',
      params: {
        email: {type: 'string'},
      },
      skipToken: true,
      async handler(ctx) {
        let {email} = ctx.params;
        const user = await this.adapter.findOne({email: email, isDeleted: false});
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_email_not_found'), 422);
        }

        if (!user.active) {
          throw new MoleculerClientError(i18next.t('error_account_not_active'), 403);
        }

        const htmlFormEmail = EmailTemplate.createForgotPasswordEmail(
          user, this.createResetPasswordLink(user._id, '5m'), '5',
        );
        this.sendEmailWithTemplate(user.email, i18next.t('email_subject_user_forgot_password'), htmlFormEmail);

        return {success: true, message: i18next.t('check_email_for_reset_link')};
      },
    },
    register: {
      rest: 'POST /register',
      params: {
        // password: { type: "string", min: 6 },
        email: {type: 'email'},
      },
      async handler(ctx) {
        const data = ctx.params;
        await this.validateEntity(data);

        delete data.active;
        delete data.isSystemAdmin;

        const checkMail = await this.adapter.findOne({email: data.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t('account_already_exists'));
        }

        const user = await this.adapter.insert(data);
        this.broker.emit('user.registered', user);

        const htmlFormEmail = EmailTemplate.createRegisterEmail(user, this.createActivateAccountLink(user._id, '365d'));
        this.sendEmailWithTemplate(user.email, i18next.t('email_subject_user_register'), htmlFormEmail);
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      },
    },
    create: {
      rest: 'POST /',
      params: {
        password: {type: 'string', min: 6},
        email: {type: 'email'},
        fullName: {type: 'string', min: 1},
      },
      admin: true,
      async handler(ctx) {
        const data = ctx.params;
        data.active = true;

        await this.validateEntity(data);

        const checkMail = await this.adapter.findOne({email: data.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t('user_email_has_registered'));
        }

        const user = await this.adapter.insert(data);
        this.broker.emit('user.registered', user);

        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      },
    },
    changePassword: {
      rest: 'POST /changePassword',
      params: {
        oldPassword: {type: 'string', min: 1},
        newPassword: {type: 'string', min: 1},
      },
      auth: 'required',
      skipToken: true,
      async handler(ctx) {
        let {oldPassword, newPassword, currentRefreshToken} = ctx.params;
        let {userID} = ctx.meta;
        const user = await this.adapter.findOne({isDeleted: false, _id: userID});
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_old_password_wrong'), 400);
        }

        const changeTime = new Date();

        const authenticated = comparePassword(oldPassword, user.password);
        if (!authenticated) {
          throw new MoleculerClientError(i18next.t('error_old_password_wrong'), 400);
        }

        const encryptedPass = encryptPassword(newPassword);

        let userUpdate = await this.adapter.updateById(
          userID,
          {
            password: encryptedPass,
            lastChangePassword: changeTime,
          },
        );

        if (userUpdate) {
          await this.timeout(1000);
          userUpdate.accessToken = jwt.issue({id: user?.id, isUser: user?.isUser}, '24h', this.settings.SECRET);
        }

        if (currentRefreshToken) {
          await ctx.call('refreshtokens.deleteMany', {
            userId: user?._id,
            refreshToken: {$ne: currentRefreshToken},
          });
        } else {
          await ctx.call('refreshtokens.deleteMany', {userId: user?._id});
        }

        const htmlFormEmail = EmailTemplate.createChangePasswordEmail(user, changeTime);
        this.sendEmailWithTemplate(user.email, i18next.t('error_user_change_message_successful'), htmlFormEmail);
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, userUpdate);
      },
    },
    resetPassword: {
      rest: 'POST /resetPassword',
      auth: 'required',
      skipToken: true,
      async handler(ctx) {
        const user = await this.adapter.findOne({isDeleted: false, _id: ctx.meta.userID});
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'));
        }
        const encryptedPass = encryptPassword(ctx.params.password);

        const userUpdate = await this.adapter.updateById(user._id, {password: encryptedPass}, {new: true});

        const htmlFormEmail = EmailTemplate.createChangePasswordEmail(userUpdate, changeTime);
        this.sendEmailWithTemplate(user.email, i18next.t('change_password_successfully'), htmlFormEmail);
        return {success: true, message: i18next.t('reset_passwo8rd_successfully')};
      },
    },
    resolveToken: {
      cache: {
        keys: ['token'],
        ttl: 30 * 60 * 60 * 24, // 30 day
      },
      params: {
        accessToken: 'string',
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(ctx.params.accessToken, this.settings.SECRET);
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          return this.transformDocuments(ctx, {}, user);
        }
      },
    },
    resolveResetPasswordToken: {
      cache: {
        keys: ['token'],
        ttl: 60 * 5, // 5 minutes
      },
      params: {
        resetPasswordToken: 'string',
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(ctx.params.resetPasswordToken, this.settings.RESET_PASSWORD_SECRET);
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          return this.transformDocuments(ctx, {}, user);
        }
      },
    },
    resolveActivationToken: {
      cache: {
        keys: ['token'],
        ttl: 60 * 60 * 24 * 365, // 1 year
      },
      params: {
        activationToken: 'string',
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(ctx.params.activationToken, this.settings.ACTIVATION_SECRET);
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          return this.transformDocuments(ctx, {}, user);
        }
      },
    },
    me: {
      rest: 'GET /me',
      auth: 'required',
      skipUser: true,
      skipToken: true,
      async handler(ctx) {
        const user = await this.getById(ctx.meta.user._id);
        if (!user) throw new MoleculerClientError(i18next.t('error_user_not_found'), 400);
        const hasPassword = !!user.password;

        const userTransform = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);

        return {
          ...userTransform,
          hasPassword,
        };
      },
    },
    logout: {
      rest: 'GET /logout',
      skipToken: true,
      async handler(ctx) {
        this.setTokenToCookies(ctx, '', '');
        return {success: true};
      },
    },
    // updateInfo: {
    //   rest: "PATCH /info",
    //   auth: "required",
    //   async handler(ctx) {
    //     const id = ctx.meta.user._id;
    //     const value = ctx.params;
    //
    //     delete value.hasPassword;
    //     delete value.password;
    //     delete value.isSystemAdmin;
    //     delete value.active;
    //     delete value.isDeleted;
    //
    //     const checkMail = await this.adapter.findOne({_id: {$ne: id}, email: value.email}, {_id: 1});
    //     if (checkMail) {
    //       throw new MoleculerClientError(i18next.t('user_email_has_registered'), 422);
    //     }
    //     if (value.phone) {
    //       const checkExistPhone = await this.adapter.findOne({
    //         phone: value.phone, isDeleted: false, _id: {$ne: id},
    //       });
    //       if (checkExistPhone) {
    //         throw new MoleculerClientError(i18next.t("phone_number_already_exists"));
    //       }
    //     }
    //     const user = await this.adapter.updateById(id, value, {new: true});
    //
    //     if (!user) {
    //       throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
    //     }
    //
    //     if (user.email) {
    //       let mailOptions = {
    //         from: `${i18next.t("email_user_create_from")} <${config.mail.auth.user}>`, // sender address
    //         to: value.email, // list of receivers
    //         subject: i18next.t("account_information_updated_successfully"), // Subject line
    //         //text: 'Pass moi la 123455', // plaintext body
    //         html: `<h2>${i18next.t("account_information")}</h2>
    //           <div><strong>${i18next.t("full_name")}: </strong>${user.fullName}</div>
    //           <div><strong>${i18next.t("phone")}: </strong>${user.phone || ""}</div>
    //           <div><strong>${i18next.t("email")}: </strong>${user.email}</div>
    //           <div>${i18next.t("sign_in")} <a href="${config.domain}">Link</a></div>`, // html body
    //       };
    //       sendEmail(mailOptions, (err) => {
    //         if (err) {
    //           console.log(err);
    //         }
    //       });
    //     }
    //     return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user,);
    //   },
    // },
    remove: {
      rest: 'DELETE /:id',
      auth: 'required',
      params: {
        id: {type: 'string', min: 3},
      },
      admin: true,
      async handler(ctx) {
        const {id} = ctx.params;
        let user = await this.adapter.findById(id);
        if (user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t('error_delete_sysadmin'));
        }

        return await this.adapter.updateById(id, {isDeleted: true});
      },
    },
    update: {
      rest: 'PUT /:id',
      auth: 'required',
      params: {
        id: {type: 'string', min: 3},
      },
      admin: true,
      async handler(ctx) {
        const value = ctx.params;

        const checkMail = await this.adapter.findOne({_id: {$ne: value.id}, email: value.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t('user_email_has_registered'), 422);
        }

        const user = await this.adapter.updateById(value.id, value, {new: true});

        if (!user) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }

        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      },
    },
    generateAccessToken: {
      rest: 'POST /generateAccessToken',
      async handler(ctx) {
        const {refreshToken} = ctx.meta;
        if (!refreshToken) {
          throw new MoleculerClientError(i18next.t('error_unauthorized'), 401);
        }
        const decoded = await jwt.verifyToken(refreshToken, this.settings.SECRET);
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          const accessToken = jwt.issue({id: user?._id, isUser: true}, '72h', this.settings.SECRET);
          this.setTokenToCookies(ctx, accessToken, refreshToken);
        }
      },
    },
    getOneByEmail: {
      rest: 'GET /getOneByEmail',
      async handler(ctx) {
        const {email} = ctx.params;
        const user = await this.adapter.findOne({email});
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      },
    },
    active: {
      rest: 'POST /activeAccount',
      skipToken: true,
      async handler(ctx) {
        const user = await this.adapter.findOne({isDeleted: false, _id: ctx.meta.userID});
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }
        if (user.active) {
          throw new MoleculerClientError(i18next.t('activation_account_has_expired'), 400);
        }

        const userUpdate = await this.adapter.updateById(user._id, {active: true}, {new: true});
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, userUpdate);
      },
    },
    resendActiveAccountLink: {
      rest: 'POST /resendActiveAccountLink',
      async handler(ctx) {
        const {email} = ctx.params;
        const user = await this.adapter.findOne({email});
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }
        if (user.active) {
          throw new MoleculerClientError(i18next.t('account_has_been_activated'), 400);
        }

        const htmlFormEmail = EmailTemplate.createActiveAccountEmail(
          user, this.createActivateAccountLink(user._id, '365d'),
        );
        this.sendEmailWithTemplate(user.email, i18next.t('email_subject_user_forgot_password'), htmlFormEmail);

        return {success: true, message: i18next.t('check_email_for_active_account_link')};
      },
    },
  },
  methods: {
    async refreshTokenCreator(ctx, user) {
      if (!user) {
        throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
      }
      let expRefreshToken;
      const nowTimeStamp = new Date().getTime();
      let expTimeStamp = nowTimeStamp + 30 * 24 * 60 * 60 * 1000;

      const nextExpDay = moment(new Date(expTimeStamp))
        .add(1, 'days')
        .format('YYYY-MM-DD 18:00:00');
      const expDay = moment(new Date(expTimeStamp)).format('YYYY-MM-DD 18:00:00');
      const nextExpDayTimeStamp = new Date(nextExpDay).getTime();
      const expDayTimeStamp = new Date(expDay).getTime();

      let expiresDateTime;
      if (expTimeStamp > expDayTimeStamp) {
        expRefreshToken = nextExpDayTimeStamp - nowTimeStamp;
        expiresDateTime = nextExpDay;
      } else {
        expRefreshToken = expDayTimeStamp - nowTimeStamp;
        expiresDateTime = expDay;
      }

      const refreshToken = jwt.issue({id: user?._id, isUser: true}, expRefreshToken / 1000 + 's',
        this.settings.SECRET,
      );
      await ctx.call('refreshtokens.create', {
        userId: user._id,
        refreshToken: refreshToken,
        expiresDate: expiresDateTime,
      });
      return refreshToken;
    },
    async getGoogleUser(id_token, access_token) {
      const {data} = await axios.get(
        'https://www.googleapis.com/oauth2/v1/userinfo',
        {
          params: {
            access_token,
            alt: 'json',
          },
          headers: {
            Authorization: `Bearer ${id_token}`,
          },
        },
      );
      return data;
    },
    async getOauthGoogleToken(code) {
      try {
        const body = {
          code,
          client_id: config.client_id,
          client_secret: config.client_secret,
          redirect_uri: config.redirect_uri,
          grant_type: 'authorization_code',
          access_type: 'offline',
        };
        const {data} = await axios.post(
          'https://oauth2.googleapis.com/token',
          body,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );
        return data;
      } catch (e) {
        console.error(e);
        throw new MoleculerClientError(i18next.t('error_google_login'), 422);
      }
    },
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
    async seedDB() {
      this.logger.info('Seed Users DB...');
      // Create system admin
      const systemAdmin = {
        email: '<EMAIL>',
        password: '123456',
        fullName: 'System Admin',
        isSystemAdmin: true,
        active: true,
      };
      await this.adapter.insert(systemAdmin);

      this.logger.info(`Generated system admin user!`);
      return this.clearCache();
    },
    createResetPasswordLink(userId, expiresIn) {
      const resetPasswordToken = jwt.issue({id: userId}, expiresIn, this.settings.RESET_PASSWORD_SECRET);
      return config.domain + '/reset-password?resetPasswordToken=' + resetPasswordToken;
    },
    createActivateAccountLink(userId, expiresIn) {
      const activationToken = jwt.issue({id: userId}, expiresIn, this.settings.ACTIVATION_SECRET);
      return config.domain + '/active-account?activationToken=' + activationToken;
    },
    sendEmailWithTemplate(email, subject, formHtml) {
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`,
        to: email,
        subject: subject,
        html: formHtml,
      };
      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });
    },
    setTokenToCookies(ctx, accessToken, refreshToken) {
      const cookieOptions = {
        httpOnly: true,
        secure: true,
        path: '/',
        maxAge: 30 * 24 * 60 * 60, // 30 day
      };
      const cookieAccessToken = cookie.serialize('accessToken', accessToken, cookieOptions);
      const cookieRefreshToken = cookie.serialize('refreshToken', refreshToken, cookieOptions);

      ctx.meta.$responseHeaders = {
        'Set-Cookie': [cookieAccessToken, cookieRefreshToken],
      };
    },
  },
  events: {},
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },
};
