const DbMongoose = require('../../mixins/dbMongo.mixin');
const ContentModel = require('./contents.model');
const BaseService = require('../../mixins/baseService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');

const {INPUT_TYPE, OUTPUT_TYPE} = require('../../constants/constant');

module.exports = {
  name: 'contents',
  mixins: [DbMongoose(ContentModel), BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {
      'toolId': 'tools.get',
      'toolId.instructionIds': 'instructions.get',
    },
    populateOptions: ['toolId', 'toolId.instructionIds'],
  },

  hooks: {},

  actions: {
    create: {
      rest: 'POST /',
      async handler(ctx) {
        const entity = ctx.params;
        entity.ownerId = ctx.meta?.user?._id;
        return this.adapter.insert(entity);
      },
    },
    details: {
      rest: 'GET /:id/details',
      async handler(ctx) {
        const {id} = ctx.params;
        const contents = await this.adapter.findOne({_id: id, isDeleted: false});
        if (!contents) {
          throw new MoleculerClientError(i18next.t('content_not_found'), 404, 'NOT_FOUND');
        }

        const contentTrans = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, contents);
        const actions = [
          {action: 'inputs.find', params: {query: {contentId: id, isDeleted: false}}},
          {
            action: 'responses.find', params: {
              query: {contentId: id, isDeleted: false},
              fields: ['_id', 'contentId', 'inputId', 'toolId', 'isActivate', 'state', 'output', 'examOrder',
                'outputType', 'plaintext', 'rating', 'headline', 'isDeleted'],
            },
          },
        ];
        const [inputs, responses] = await ctx.mcall(actions);

        return {...contentTrans, inputs, responses};
      },
    },
    remove: {
      rest: 'DELETE /:id',
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const content = await this.adapter.findById(id);
        await this.broker.emit('contents.deteted', content, ctx);
        await this.updateContentIndex(content);
        ctx.emit('lastModifiedProject', {id: content?.projectId});
        this.broker.emit('recentProject', {projectId: content?.projectId, userId: ctx.meta?.user?._id});
        return await this.adapter.updateById(id, {isDeleted: true}, {new: true});
      },
    },
    submit: {
      rest: 'PUT /:id/submit',
      params: {
        inputData: {type: 'object', required: true},
        inputType: {type: 'string'},
      },
      async handler(ctx) {
        const {inputData, inputType, id} = ctx.params;

        const content = await this.adapter.findById(id);

        const inputEntity = {
          inputData,
          inputType,
          toolId: content.toolId,
          contentId: content._id,
          plaintext: await this.getInputPlaintext(inputType, inputData),
        };
        const savedInput = await ctx.call('inputs.insert', {entity: inputEntity});

        const processingResponse = {
          inputId: savedInput._id,
          contentId: content._id,
          output: {
            text: 'Hold on! We are processing',
          },
          state: 'processing',
          isActivate: true,
        };

        const savedResponse = await ctx.call('responses.insert', {entity: processingResponse});

        return this.responseSubmit(ctx, savedResponse, content, savedInput);
      },
    },
    submitInput: {
      rest: 'POST /submitInput',
      params: {
        input: 'object',
        response: 'object',
      },
      async handler(ctx) {
        let {input, response} = ctx.params;
        try {
          const processingResponse = await ctx.call(`tools.submit`, {input, response});

          if (processingResponse?.success) {
            response = {
              ...response,
              ...processingResponse,
              state: 'done',
            };
            delete response.isDeleted;
            await ctx.call('responses.updateStreamResponse', {
              ...response,
              id: response._id,
            });
          } else {
            response = {
              ...response,
              output: {error: processingResponse, message: 'System busy please try again!'},
              state: 'error',
            };
            await ctx.call('responses.updateStreamResponse', {
              ...response,
              id: response._id,
            });
          }
          await ctx.emit(`sse.${response._id}`, response);
        } catch (error) {
          console.log(error);
          response = {
            ...response,
            output: {error, message: 'System busy please try again!'},
            state: 'error',
          };
          await ctx.call('responses.updateStreamResponse', {
            ...response,
            id: response._id,
          });
          await ctx.emit(`sse.${response._id}`, response);
        }

        return response;
      },
    },
  },
  methods: {
    async responseSubmit(ctx, response, content, input) {
      const {inputData, inputType} = input;

      ctx.call('contents.submitInput', {input, response});

      // ctx.emit('recentProject', {projectId: content.projectId, userId: ctx.meta.user?._id});
      // ctx.emit('resourceUpdate', {inputData, inputType, projectId: content.projectId});
      // ctx.emit('imageUpdateSubmit', {inputData});
      // await ctx.emit('userSubmited', {inputType, workspaceId});
      return ctx.call('responses.get', {id: response._id, populate: ['inputId']});
    },
    async updateContentIndex(content) {
      const {_id: id, projectId} = content;
      const foundContents = await this.adapter.find({
        query: {projectId, isDeleted: false, _id: {$ne: id}},
        sort: 'contentIndex',
      });
      const mcallContents = foundContents.map(({_id}, index) => ({
        action: 'contents.update',
        params: {contentIndex: index + 1, id: _id},
      }));

      await this.broker.mcall(mcallContents);
    },
    async getInputPlaintext(inputType, inputData) {
      switch (inputType) {
        case INPUT_TYPE.TEXT:
        case INPUT_TYPE.TTS:
          return `Text: ${inputData.text}`;
        case INPUT_TYPE.VIDEO:
        case INPUT_TYPE.AUDIO:
          return this.getVideoInput(inputData);
        case INPUT_TYPE.TOPIC:
          return `Topic: ${inputData.topic}`;
        default:
          return '';
      }
    },
    async getVideoInput(inputData) {
      const cutStartMs = this.secondsToMS(inputData.cutStart);
      const cutEndMs = this.secondsToMS(inputData.cutEnd);
      const timeRange = `Time start: ${cutStartMs} - time end: ${cutEndMs}\n`;
      if (inputData.url) {
        return timeRange + `Link youtube: ${inputData.url}`;
      }
      if (inputData.offlineVideoId) {
        const file = await this.broker.call('offlinevideos.get', {id: inputData.offlineVideoId.toString()});
        return timeRange + `Video: ${file.name}`;
      }
      if (inputData.audioId) {
        const file = await this.broker.call('files.get', {id: inputData.audioId.toString()});
        return timeRange + `Audio: ${file.displayName}`;
      }

    },
    getServiceName(modelInterface = null) {
      const serviceMap = {
        ClaudeAI: 'claudeai',
        AzureOpenAI: 'azureopenai',
        AWSBedrock: 'awsbedrock',
        GoogleGenerativeAI: 'gemini',
        ChatOpenAI: 'chatgpt',
        VertexAI: 'vertexai',
      };

      return serviceMap[modelInterface] || 'azureopenai';
    },
  },
  events: {
    async chatAi(socket, sender, event, ctx) {
      const prompt = [];
      const [tool] = await this.broker.call('tools.find', {query: {urlName: 'ai-assistant'}});
      const content = await this.broker.call('contents.create', {
        toolId: tool._id,
        ownerId: ctx.meta?.user?._id,
      });
      const response = await this.broker.call('responses.create', {contentId: content._id});
      let completionTokens = 0, promptTokens = 0, totalTokens = 0;

      socket.on('message', async (param) => {
        const {model, message} = param;

        prompt.push({role: 'user', content: message});
        try {
          const {maxTokens, modelInterface, configKey} = await this.broker.call('modalais.getOneByModel', {model});
          const serviceName = this.getServiceName(modelInterface);

          const completion = await this.broker.call(`${serviceName}.completion`, {model, maxTokens, prompt, configKey});

          completionTokens += completion.usageMetadata.candidatesTokenCount;
          promptTokens += completion.usageMetadata.promptTokenCount;
          totalTokens += completion.usageMetadata.candidatesTokenCount;

          socket.emit('response', completion.candidates[0].content.parts[0].text);
          prompt.push({role: 'assistant', content: completion.candidates[0].content.parts[0].text});
        } catch (error) {
          socket.emit('response', {error: 'This model is not supported.'});
        }
      });

      socket.on('disconnect', async () => {
        this.broker.call('responses.update', {
          id: response._id,
          lastMessages: prompt,
          state: 'done',
          completionTokens,
          promptTokens,
          totalTokens,
        });
        console.log(`Client disconnected ${socket.id}`);
      });
    },
  },
};
