const Model = require('./tools.model');
const DbMongoose = require('../../mixins/dbMongo.mixin');
const BaseService = require('../../mixins/baseService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const {completionOptionCreator} = require('./aitool/promptengine');
const {getConfig} = require('../../config/config');
const i18next = require('i18next');
const {INPUT_TYPE, OUTPUT_TYPE} = require('../../constants/constant');
const config = getConfig(process.env.NODE_ENV);
const {MoleculerClientError} = require('moleculer').Errors;
const AiTools = require('./tools.ai');

module.exports = {
  name: 'tools',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AiTools],
  settings: {
    entityValidator: {},
    populates: {
      instructionIds: {
        action: 'instructions.get',
        params: {
          fields: 'shortName outputType _id showAdditionalRequest outputTypeId optionIds',
        },
      },
      groupToolIds: 'toolgroups.get',
    },
    populateOptions: [
      'instructionIds.outputTypeId', 'instructionIds.optionIds', 'groupToolIds',
    ],
  },
  dependencies: [],

  actions: {
    copy: {
      rest: 'POST /copy',
      admin: true,
      async handler(ctx) {
        const {toolId} = ctx.params;

        const tool = await this.adapter.findById(toolId);
        if (!tool) {
          throw new MoleculerClientError(i18next.t('error_tool_not_found'), 404);
        }

        const newToolObj = {
          ...tool.toObject(),
          localization: {
            name: {
              en: `${tool.localization.name.en} - Copy`,
              vi: `${tool.localization.name.en} - Sao chép`,
            },
          },
          visible: 'developing',
          _id: undefined,
          createdAt: new Date(),
          updatedAt: new Date(),
          isDeleted: false,
        };

        return await this.adapter.insert(newToolObj);
      },
    },
    remove: {
      rest: 'DELETE /:id',
      admin: true,
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const tool = await this.adapter.findOne({_id: id});
        if (!tool) {
          throw new MoleculerClientError(i18next.t('error_tool_not_found'), 404);
        }
        await this.adapter.updateById(id, {isDeleted: true}, {new: true});
        return tool;
      },
    },
    allTools: {
      rest: 'GET /allTools',
      async handler(ctx) {
        return this.getDetailTools(ctx);
      },
    },
    availableTools: {
      rest: {
        method: 'GET',
        path: '/availableTools',
      }, auth: 'required', async handler(ctx) {
        const queryTools = {
          isDeleted: false, visible: {$in: ['public', 'developing']}, groupToolIds: {$exists: true},
        };
        return this.getDetailTools(ctx, queryTools);
      },
    },
    findOneByToolId: {
      rest: 'GET /:toolId/findOne',
      params: {
        toolId: 'string',
      },
      admin: true,
      async handler(ctx) {
        const {toolId} = ctx.params;
        const tool = await this.adapter.findOne({toolId});
        return this.transformDocuments(ctx, {populate: ['instructionIds.optionIds']}, tool);
      },
    },

  },
  events: {},
  methods: {
    async seedDB() {
      const toolsDefine = require('./tools.seed.json');

      await toolsModel.bulkWrite(toolsDefine.map(row => ({
        updateOne: {
          filter: {urlName: row.urlName},
          update: {$set: {...row, isDeleted: false}},
          upsert: true,
        },
      })), {ordered: false});
    },
    async getDetailTools(ctx, queryTools = {}) {
      const {user} = ctx.meta;

      const actions = [
        {
          action: 'instructions.find', params: {query: {isDeleted: false}},
        },
        {
          action: 'tools.find', params: {
            query: queryTools,
            populate: ['instructionIds.optionIds', 'instructionIds.outputTypeId', 'groupToolIds'],
            sort: '-visible',
          },
        },
        {
          action: 'favoritetools.find', params: {query: {userId: user._id, isFavorite: true}, populate: []},
        },
      ];

      const [instructions, publicTools, userFavoriteTools] = await ctx.mcall(actions);

      const tools = publicTools.sort((a, b) => b.visible.localeCompare(a.visible));
      const favoriteToolsMap = userFavoriteTools.reduce((map, item) => {
        map[item.toolId] = item;
        return map;
      }, {});

      const instructionIds = new Set(instructions.map((instruction) => instruction._id));

      const sampleContents = await ctx.call('samplecontents.find', {
        query: {isDeleted: false}, populate: [],
      });
      const mapContent = sampleContents.reduce((map, item) => {
        map[item.toolId] = item._id;
        return map;
      }, {});

      return tools.map((tool) => {
        const filteredInstructions = tool.instructionIds
          .filter((instruction) => instructionIds.has(instruction._id))
          .map((instruction) => ({
            outputTypeId: instruction.outputTypeId,
            shortName: instruction.shortName,
            showAdditionalRequest: instruction.showAdditionalRequest,
            _id: instruction._id,
            options: instruction.optionIds || [],
          }));

        return {
          ...tool,
          instructionIds: filteredInstructions,
          isFavorite: !!favoriteToolsMap[tool._id],
          existGuide: !!mapContent[tool._id],
        };
      });
    },
  },
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },
};
